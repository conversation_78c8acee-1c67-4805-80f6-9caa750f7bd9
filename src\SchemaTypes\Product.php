<?php
namespace SlimSEOPro\Schema\SchemaTypes;

$aggregate_ratings                     = Helper::get_property( 'aggregateRating', [ 'show' => true ] );
$aggregate_ratings['fields'][0]['std'] = '{{ product.rating }}';
$aggregate_ratings['fields'][2]['std'] = '{{ product.review_count }}';

return [
	[
		'id'   => 'googleDocs',
		'type' => 'GoogleDocs',
		'url'  => 'https://developers.google.com/search/docs/advanced/structured-data/product',
		'show' => true,
	],
	Helper::get_property( 'name', [
		'required' => true,
		'tooltip'  => __( 'The name of the product', 'slim-seo-schema' ),
	] ),
	Helper::get_property( 'description', [
		'show'    => true,
		'tooltip' => __( 'The product description', 'slim-seo-schema' ),
	] ),
	[
		'label'   => __( 'Brand name', 'slim-seo-schema' ),
		'id'      => 'brand',
		'show'    => true,
		'tooltip' => __( 'The brand of the product', 'slim-seo-schema' ),
		'type'    => 'Group',
		'fields'  => [
			[
				'id'       => '@type',
				'std'      => 'Brand',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'   => 'name',
				'std'  => '{{ site.title }}',
				'show' => true,
			],
		],
	],
	Helper::get_property( 'url', [
		'show'    => true,
		'tooltip' => __( 'The URL of the product', 'slim-seo-schema' ),
		'std'     => '{{ post.url }}',
	] ),
	[
		'label'   => __( 'SKU', 'slim-seo-schema' ),
		'id'      => 'sku',
		'show'    => true,
		'std'     => '{{ product.sku }}',
		'tooltip' => __( 'The Stock Keeping Unit (SKU), i.e. a merchant-specific identifier for a product or service, or the product to which the offer refers.', 'slim-seo-schema' ),
	],
	Helper::get_property( 'image', [
		'show'    => true,
		'tooltip' => __( 'The URL of a product photo', 'slim-seo-schema' ),
		'std'     => [
			'{{ post.thumbnail }}',
		],
	] ),
	Helper::get_property( 'Review', [
		'fields' => [
			[
				'id' => '@type',
			],
			[
				'id' => 'author',
			],
			[
				'id' => 'reviewRating',
			],
			[
				'id' => 'datePublished',
			],
			[
				'id'      => 'positiveNotes',
				'type'    => 'Group',
				'label'   => __( 'Positive notes (Pros)', 'slim-seo-schema' ),
				'tooltip' => __( 'A list of positive statements about the product, listed in a specific order', 'slim-seo-schema' ),
				'show'    => true,
				'fields'  => [
					[
						'id'       => '@type',
						'std'      => 'ItemList',
						'type'     => 'Hidden',
						'required' => true,
					],
					[
						'id'        => 'itemListElement',
						'type'      => 'Group',
						'cloneable' => true,
						'show'      => true,
						'fields'    => [
							[
								'id'       => '@type',
								'std'      => 'ListItem',
								'type'     => 'Hidden',
								'required' => true,
							],
							Helper::get_property( 'name', [
								'std'      => '',
								'tooltip'  => __( 'The key statement of the review.', 'slim-seo-schema' ),
								'required' => true,
							] ),
							[
								'id'      => 'position',
								'label'   => __( 'Position', 'slim-seo-schema' ),
								'show'    => true,
								'tooltip' => __( 'The position (order) of the statement in the list.', 'slim-seo-schema' ),
							],
						],
					],
				],
			],
			[
				'id'      => 'negativeNotes',
				'type'    => 'Group',
				'label'   => __( 'Negative notes (Cons)', 'slim-seo-schema' ),
				'tooltip' => __( 'A list of negative statements about the product, listed in a specific order', 'slim-seo-schema' ),
				'show'    => true,
				'fields'  => [
					[
						'id'       => '@type',
						'std'      => 'ItemList',
						'type'     => 'Hidden',
						'required' => true,
					],
					[
						'id'        => 'itemListElement',
						'type'      => 'Group',
						'cloneable' => true,
						'show'      => true,
						'fields'    => [
							[
								'id'       => '@type',
								'std'      => 'ListItem',
								'type'     => 'Hidden',
								'required' => true,
							],
							Helper::get_property( 'name', [
								'std'      => '',
								'tooltip'  => __( 'The key statement of the review.', 'slim-seo-schema' ),
								'required' => true,
							] ),
							[
								'id'      => 'position',
								'label'   => __( 'Position', 'slim-seo-schema' ),
								'show'    => true,
								'tooltip' => __( 'The position (order) of the statement in the list.', 'slim-seo-schema' ),
							],
						],
					],
				],
			],
		],
	] ),
	$aggregate_ratings,
	[
		'id'     => 'offers',
		'type'   => 'Group',
		'label'  => __( 'Offers', 'slim-seo-schema' ),
		'show'   => true,
		'fields' => [
			[
				'id'          => '@type',
				'label'       => __( 'Type', 'slim-seo-schema' ),
				'type'        => 'DataList',
				'required'    => true,
				'std'         => 'Offer',
				'dependant'   => true,
				'placeholder' => __( 'None', 'slim-seo-schema' ),
				'options'     => [
					'Offer'          => __( 'Offer', 'slim-seo-schema' ),
					'AggregateOffer' => __( 'Aggregate Offer', 'slim-seo-schema' ),
				],
			],
			[
				'id'         => 'price',
				'label'      => __( 'Price', 'slim-seo-schema' ),
				'required'   => true,
				'tooltip'    => __( 'The offer price of a product', 'slim-seo-schema' ),
				'dependency' => '[@type]:Offer',
				'std'        => '{{ product.price }}',
			],
			[
				'id'       => 'priceCurrency',
				'label'    => __( 'Price currency', 'slim-seo-schema' ),
				'std'      => 'USD',
				'required' => true,
				'std'      => '{{ product.currency }}',
			],
			[
				'id'         => 'priceValidUntil',
				'label'      => __( 'Price valid until', 'slim-seo-schema' ),
				'type'       => 'Date',
				'tooltip'    => __( 'The date (in ISO 8601 date format) after which the price will no longer be available', 'slim-seo-schema' ),
				'dependency' => '[@type]:Offer',
				'std'        => '{{ product.sale_to }}',
			],
			[
				'id'         => 'availability',
				'label'      => __( 'Availability', 'slim-seo-schema' ),
				'type'       => 'DataList',
				'show'       => true,
				'tooltip'    => __( 'The possible product availability options', 'slim-seo-schema' ),
				'dependency' => '[@type]:Offer',
				'std'        => '{{ product.stock }}',
				'options'    => [
					'https://schema.org/Discontinued' => __( 'Discontinued', 'slim-seo-schema' ),
					'https://schema.org/InStock'      => __( 'In stock', 'slim-seo-schema' ),
					'https://schema.org/InStoreOnly'  => __( 'In store only', 'slim-seo-schema' ),
					'https://schema.org/LimitedAvailability' => __( 'Limited availability', 'slim-seo-schema' ),
					'https://schema.org/OnlineOnly'   => __( 'Online only', 'slim-seo-schema' ),
					'https://schema.org/OutOfStock'   => __( 'Out of stock', 'slim-seo-schema' ),
					'https://schema.org/PreOrder'     => __( 'Pre order', 'slim-seo-schema' ),
					'https://schema.org/PreSale'      => __( 'Pre sale', 'slim-seo-schema' ),
					'https://schema.org/SoldOut'      => __( 'Sold out', 'slim-seo-schema' ),
				],
			],
			[
				'id'         => 'itemOffered',
				'label'      => __( 'Item offered', 'slim-seo-schema' ),
				'tooltip'    => __( 'The item being sold', 'slim-seo-schema' ),
				'dependency' => '[@type]:Offer',
				'std'        => '{{ post.title }}',
			],
			[
				'id'         => 'url',
				'label'      => __( 'URL', 'slim-seo-schema' ),
				'tooltip'    => __( 'The URL of the product', 'slim-seo-schema' ),
				'dependency' => '[@type]:Offer',
				'std'        => '{{ post.url }}',
			],
			[
				'id'         => 'shippingDetails',
				'label'      => __( 'Shipping details', 'slim-seo-schema' ),
				'type'       => 'Group',
				'dependency' => '[@type]:Offer',
				'tooltip'    => __( 'Nested information about the shipping policies and options associated with an Offer', 'slim-seo-schema' ),
				'fields'     => [
					[
						'id'       => '@type',
						'std'      => 'OfferShippingDetails',
						'type'     => 'Hidden',
						'required' => true,
					],
					[
						'id'       => 'shippingDestination',
						'label'    => __( 'Destination', 'slim-seo-schema' ),
						'type'     => 'Group',
						'required' => true,
						'fields'   => [
							[
								'id'       => '@type',
								'std'      => 'DefinedRegion',
								'type'     => 'Hidden',
								'required' => true,
							],
							[
								'id'       => 'addressCountry',
								'label'    => __( 'Country code', 'slim-seo-schema' ),
								'tooltip'  => __( 'The 2-digit country code, in ISO 3166-1 format', 'slim-seo-schema' ),
								'required' => true,
							],
							[
								'id'    => 'addressRegion',
								'label' => __( 'Region', 'slim-seo-schema' ),
								'show'  => true,
							],
						],
					],
					[
						'id'      => 'deliveryTime',
						'label'   => __( 'Delivery time', 'slim-seo-schema' ),
						'type'    => 'Group',
						'tooltip' => __( 'The total delay between the receipt of the order and the goods reaching the final customer', 'slim-seo-schema' ),
						'fields'  => [
							[
								'id'       => '@type',
								'std'      => 'ShippingDeliveryTime',
								'type'     => 'Hidden',
								'required' => true,
							],
							[
								'id'      => 'handlingTime',
								'label'   => __( 'Handling time', 'slim-seo-schema' ),
								'type'    => 'Group',
								'tooltip' => __( 'The typical delay between the receipt of the order and the goods either leaving the warehouse or being prepared for pickup, in case the delivery method is on site pickup', 'slim-seo-schema' ),
								'fields'  => [
									[
										'id'       => '@type',
										'std'      => 'QuantitativeValue',
										'type'     => 'Hidden',
										'required' => true,
									],
									[
										'id'    => 'minValue',
										'label' => __( 'Min value', 'slim-seo-schema' ),
										'show'  => true,
									],
									[
										'id'    => 'maxValue',
										'label' => __( 'Max value', 'slim-seo-schema' ),
										'show'  => true,
									],
								],
							],
							[
								'id'      => 'transitTime',
								'label'   => __( 'Transit time', 'slim-seo-schema' ),
								'type'    => 'Group',
								'tooltip' => __( 'The typical delay the order has been sent for delivery and the goods reach the final customer.', 'slim-seo-schema' ),
								'fields'  => [
									[
										'id'       => '@type',
										'std'      => 'QuantitativeValue',
										'type'     => 'Hidden',
										'required' => true,
									],
									[
										'id'    => 'minValue',
										'label' => __( 'Min value', 'slim-seo-schema' ),
										'show'  => true,
									],
									[
										'id'    => 'maxValue',
										'label' => __( 'Max value', 'slim-seo-schema' ),
										'show'  => true,
									],
								],
							],
							[
								'id'      => 'cutOffTime',
								'label'   => __( 'Cutoff time', 'slim-seo-schema' ),
								'show'    => true,
								'tooltip' => __( 'The time after which new orders are no longer processed on that same day, in ISO 8601 format. One day gets added to the handling time.', 'slim-seo-schema' ),
							],
							Helper::get_property( 'OpeningHoursSpecification', [
								'id'               => 'businessDays',
								'label'            => __( 'Business days', 'slim-seo-schema' ),
								'cloneItemHeading' => __( 'Business days', 'slim-seo-schema' ),
							] ),
						],
					],
					[
						'id'      => 'doesNotShip',
						'label'   => __( 'Does Not Ship?', 'slim-seo-schema' ),
						'type'    => 'DataList',
						'options' => [
							'true'  => __( 'Yes', 'slim-seo-schema' ),
							'false' => __( 'No', 'slim-seo-schema' ),
						],
					],
					[
						'id'      => 'shippingRate',
						'label'   => __( 'Shipping rate', 'slim-seo-schema' ),
						'type'    => 'Group',
						'tooltip' => __( 'Information about the cost of shipping to the specified destination', 'slim-seo-schema' ),
						'fields'  => [
							[
								'id'       => '@type',
								'std'      => 'MonetaryAmount',
								'type'     => 'Hidden',
								'required' => true,
							],
							[
								'id'    => 'currency',
								'label' => __( 'Currency', 'slim-seo-schema' ),
								'show'  => true,
							],
							[
								'id'    => 'value',
								'label' => __( 'Value', 'slim-seo-schema' ),
								'show'  => true,
							],
						],
					],
				],
			],
			[
				'id'         => 'lowPrice',
				'label'      => __( 'Low price', 'slim-seo-schema' ),
				'required'   => true,
				'tooltip'    => __( 'The lowest price of all offers available. Use a floating point number.', 'slim-seo-schema' ),
				'dependency' => '[@type]:AggregateOffer',
				'std'        => '{{ product.low_price }}',
			],
			[
				'id'         => 'highPrice',
				'label'      => __( 'High price', 'slim-seo-schema' ),
				'dependency' => '[@type]:AggregateOffer',
				'std'        => '{{ product.high_price }}',
			],
			[
				'id'         => 'offerCount',
				'label'      => __( 'Offer count', 'slim-seo-schema' ),
				'tooltip'    => __( 'The number of offers for the product.', 'slim-seo-schema' ),
				'dependency' => '[@type]:AggregateOffer',
				'std'        => '{{ product.offer_count }}',
			],
		],
	],

	// Optional properties.
	[
		'label'   => __( 'Audience', 'slim-seo-schema' ),
		'id'      => 'audience',
		'tooltip' => __( 'An intended audience, i.e. a group for whom something was created.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Award', 'slim-seo-schema' ),
		'id'      => 'award',
		'tooltip' => __( 'An award won by or for this item.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Category', 'slim-seo-schema' ),
		'id'      => 'category',
		'tooltip' => __( 'A category for the item. Greater signs or slashes can be used to informally indicate a category hierarchy.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Color', 'slim-seo-schema' ),
		'id'      => 'color',
		'tooltip' => __( 'The color of the product.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Country of assembly', 'slim-seo-schema' ),
		'id'      => 'countryOfAssembly',
		'tooltip' => __( 'The place where the product was assembled.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Country of last processing', 'slim-seo-schema' ),
		'id'      => 'countryOfLastProcessing',
		'tooltip' => __( 'The place where the product was last processed and tested before importation.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Country of origin', 'slim-seo-schema' ),
		'id'      => 'countryOfOrigin',
		'type'    => 'Group',
		'tooltip' => __( 'The country of origin of something, including products as well as creative works such as movie and TV content.', 'slim-seo-schema' ),
		'fields'  => [
			[
				'id'   => '@type',
				'std'  => 'Country',
				'type' => 'Hidden',
			],
			[
				'id'      => 'address',
				'label'   => __( 'Address', 'slim-seo-schema' ),
				'tooltip' => __( 'Physical address of the item.', 'slim-seo-schema' ),
			],
			[
				'id'      => 'branchCode',
				'label'   => __( 'Branch code', 'slim-seo-schema' ),
				'tooltip' => __( 'A short textual code (also called "store code") that uniquely identifies a place of business. The code is typically assigned by the parentOrganization and used in structured URLs.', 'slim-seo-schema' ),
			],
			[
				'id'      => 'description',
				'label'   => __( 'Description', 'slim-seo-schema' ),
				'tooltip' => __( 'A description of the country.', 'slim-seo-schema' ),
			],
			[
				'id'    => 'faxNumber',
				'label' => __( 'Fax number', 'slim-seo-schema' ),
			],
			[
				'id'      => 'globalLocationNumber',
				'label'   => __( 'Global location number', 'slim-seo-schema' ),
				'tooltip' => __( 'The GLN is a 13-digit number used to identify parties and physical locations.', 'slim-seo-schema' ),
			],
			[
				'id'      => 'hasMap',
				'label'   => __( 'Has Map', 'slim-seo-schema' ),
				'tooltip' => __( 'A URL to a map of the place.', 'slim-seo-schema' ),
			],
			[
				'id'      => 'latitude',
				'label'   => __( 'Latitude', 'slim-seo-schema' ),
				'tooltip' => __( 'The latitude of a location. For example 37.42242 (WGS 84).', 'slim-seo-schema' ),
			],
			[
				'id'      => 'longitude',
				'label'   => __( 'Longitude', 'slim-seo-schema' ),
				'tooltip' => __( 'The longitude of a location. For example -122.08585 (WGS 84).', 'slim-seo-schema' ),
			],
			[
				'id'      => 'logo',
				'label'   => __( 'Logo', 'slim-seo-schema' ),
				'tooltip' => __( 'Link to an associated logo.', 'slim-seo-schema' ),
			],
			[
				'id'      => 'name',
				'label'   => __( 'Name', 'slim-seo-schema' ),
				'tooltip' => __( 'The name of the country.', 'slim-seo-schema' ),
				'show'    => true,
			],
			[
				'id'    => 'telephone',
				'label' => __( 'Telephone number', 'slim-seo-schema' ),
			],
		],
	],
	[
		'label'   => __( 'Depth', 'slim-seo-schema' ),
		'id'      => 'depth',
		'std'     => '{{ post.custom_field._length }} cm',
		'tooltip' => __( 'The depth of the product.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Disambiguating description', 'slim-seo-schema' ),
		'id'      => 'disambiguatingDescription',
		'tooltip' => __( 'A sub property of description. A short description of the item used to disambiguate from other.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'GTIN', 'slim-seo-schema' ),
		'id'      => 'gtin',
		'tooltip' => __( 'A Global Trade Item Number (GTIN). GTINs identify trade items, including products and services, using numeric identification codes.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'GTIN-8', 'slim-seo-schema' ),
		'id'      => 'gtin8',
		'tooltip' => __( 'The GTIN-8 code of the product, or the product to which the offer refers. This code is also known as EAN/UCC-8 or 8-digit EAN.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'GTIN-12', 'slim-seo-schema' ),
		'id'      => 'gtin12',
		'tooltip' => __( 'The GTIN-12 code of the product, or the product to which the offer refers. The GTIN-12 is the 12-digit GS1 Identification Key composed of a U.P.C. Company Prefix, Item Reference, and Check Digit used to identify trade items.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'GTIN-13', 'slim-seo-schema' ),
		'id'      => 'gtin13',
		'tooltip' => __( 'The GTIN-13 code of the product, or the product to which the offer refers. This is equivalent to 13-digit ISBN codes and EAN UCC-13. Former 12-digit UPC codes can be converted into a GTIN-13 code by simply adding a preceding zero.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'GTIN-14', 'slim-seo-schema' ),
		'id'      => 'gtin14',
		'tooltip' => __( 'The GTIN-14 code of the product, or the product to which the offer refers.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Height', 'slim-seo-schema' ),
		'id'      => 'height',
		'std'     => '{{ post.custom_field._height }} cm',
		'tooltip' => __( 'The height of the product.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Logo', 'slim-seo-schema' ),
		'id'      => 'logo',
		'tooltip' => __( 'Link to logo associated with the product.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'manufacturer',
		'label'   => __( 'Manufacturer', 'slim-seo-schema' ),
		'tooltip' => __( 'The manufacturer of the product.', 'slim-seo-schema' ),
		'std'     => '{{ schemas.organization }}',
	],
	[
		'label'   => __( 'Material', 'slim-seo-schema' ),
		'id'      => 'material',
		'tooltip' => __( 'A material that product is made from, e.g. leather, wool, cotton, paper.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Model', 'slim-seo-schema' ),
		'id'      => 'model',
		'tooltip' => __( 'The model of the product.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'MPN', 'slim-seo-schema' ),
		'id'      => 'mpn',
		'tooltip' => __( 'The Manufacturer Part Number (MPN) of the product, or the product to which the offer refers', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'NSN', 'slim-seo-schema' ),
		'id'      => 'nsn',
		'tooltip' => __( 'Indicates the NATO stock number (nsn) of a Product.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Pattern', 'slim-seo-schema' ),
		'id'      => 'pattern',
		'tooltip' => __( 'A pattern that something has, for example "polka dot", "striped".', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Product ID', 'slim-seo-schema' ),
		'id'      => 'productID',
		'tooltip' => __( 'The product identifier, such as ISBN.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Product group ID', 'slim-seo-schema' ),
		'id'      => 'inProductGroupWithID',
		'tooltip' => __( 'The id of a product group that this product variant belongs to. See also Item Group Id in Google Merchant Center Help. At most one value should be specified.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Production date', 'slim-seo-schema' ),
		'id'      => 'productionDate',
		'std'     => '{{ product.date }}',
		'tooltip' => __( 'The date of production of product in ISO 8601 date format.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Purchase date', 'slim-seo-schema' ),
		'id'      => 'purchaseDate',
		'std'     => '{{ product.date }}',
		'tooltip' => __( 'The date the item e.g. vehicle was purchased by the current owner in ISO 8601 date format.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Release date', 'slim-seo-schema' ),
		'id'      => 'releaseDate',
		'std'     => '{{ product.date }}',
		'tooltip' => __( 'The release date of a product or product model in ISO 8601 date format. This can be used to distinguish the exact variant of a product.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Size', 'slim-seo-schema' ),
		'id'      => 'size',
		'show'    => false,
		'tooltip' => __( 'A standardized size of a product or creative work, specified either through a simple textual string (for exp. "XL", "32Wx34L").', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Slogan', 'slim-seo-schema' ),
		'id'      => 'slogan',
		'tooltip' => __( 'A slogan or motto associated with the product.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Weight', 'slim-seo-schema' ),
		'id'      => 'weight',
		'std'     => '{{ post.custom_field._weight }} kg',
		'tooltip' => __( 'The weight of the product.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Width', 'slim-seo-schema' ),
		'id'      => 'width',
		'std'     => '{{ post.custom_field._width }} cm',
		'tooltip' => __( 'The width of the product.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Item condition', 'slim-seo-schema' ),
		'id'      => 'itemCondition',
		'type'    => 'DataList',
		'tooltip' => __( 'The condition of the product or service. Also used for product return policies to specify the condition of products accepted for returns.', 'slim-seo-schema' ),
		'std'     => 'https://schema.org/NewCondition',
		'options' => [
			'https://schema.org/NewCondition'         => __( 'New condition', 'slim-seo-schema' ),
			'https://schema.org/UsedCondition'        => __( 'Used condition', 'slim-seo-schema' ),
			'https://schema.org/RefurbishedCondition' => __( 'Refurbished condition', 'slim-seo-schema' ),
			'https://schema.org/DamagedCondition'     => __( 'Damaged condition', 'slim-seo-schema' ),
		],
	],
];
