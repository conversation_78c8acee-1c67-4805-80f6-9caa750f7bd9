<?php
namespace SlimSEOPro\Schema\SchemaTypes;

return [
	[
		'id'   => 'googleDocs',
		'type' => 'GoogleDocs',
		'url'  => 'https://developers.google.com/search/docs/advanced/structured-data/math-solvers',
		'show' => true,
	],
	Helper::get_property( 'name', [
		'show'    => true,
		'tooltip' => __( 'The name of the item.', 'slim-seo-schema' ),
	] ),
	[
		'id'      => 'inLanguage',
		'label'   => __( 'In language', 'slim-seo-schema' ),
		'tooltip' => __( 'The language(s) that are supported by your math problem solving site.', 'slim-seo-schema' ),
		'std'     => 'en',
		'show'    => true,
	],
	[
		'id'               => 'potentialAction',
		'label'            => __( 'Potential action', 'slim-seo-schema' ),
		'type'             => 'Group',
		'required'         => true,
		'cloneable'        => true,
		'cloneItemHeading' => __( 'Action', 'slim-seo-schema' ),
		'tooltip'          => __( 'The action that leads to a mathematical explanation of a math expression.', 'slim-seo-schema' ),
		'fields'           => [
			[
				'id'       => '@type',
				'std'      => 'SolveMathAction',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'      => 'eduQuestionType',
				'label'   => __( 'Question type', 'slim-seo-schema' ),
				'tooltip' => __( 'The problem type(s) that are capable of being solved by the target property.', 'slim-seo-schema' ),
				'type'    => 'DataList',
				'show'    => true,
				'options' => [
					'Absolute Value Equation'   => __( 'Absolute value equations. For exp. |x - 5| = 9' ),
					'Algebra'                   => __( 'A generic problem type that can be placed with other problem type. For exp. polynomial equations, exponential equations, and radical expressions.' ),
					'Arc Length'                => __( 'Arc length problems. For exp. Determine the length of x = 4 (3 + y)^2, 1 < y < 4.' ),
					'Arithmetic'                => __( 'Arithmetic problems. For exp. Find the sum of 5 + 7.' ),
					'Biquadratic Equation'      => __( 'Biquadratic equations. For exp. x^4 - x^2 - 2 = 0.' ),
					'Calculus'                  => __( 'A generic problem type that can be placed with other problem types. For exp. integrals, derivatives, and differential equations.' ),
					'Characteristic Polynomial' => __( 'Find the characteristic polynomial of {{1,2,5}, {3,-1,1}, {1,2,3}}.' ),
					'Circle'                    => __( 'Circle related problems. For exp. Find the radius of x^2 + y^2 = 3.' ),
					'Derivative'                => __( 'Derivative of 5x^4 + 2x^3 + 4x - 2.' ),
					'Differential Equation'     => __( 'Differential equation problems. For exp. y+dy/dx=5x.' ),
					'Distance'                  => __( 'Distance problems. For exp. Find the distance between (6,-1) and (-3,2).' ),
					'Eigenvalue'                => __( 'Eigenvalue problems. For exp. Find the eigenvalues for the matrix [[-6, 3], [4, 5]].' ),
					'Eigenvector'               => __( 'Eigenvector problems. For exp. Find the eigenvector for the matrix [[-6, 3], [4, 5]] with eigenvalues of [-7, 6].' ),
					'Ellipse'                   => __( 'Ellipse problems. For exp. Find the x and y intercepts of 9x^2 + 4y^2 = 36.' ),
					'Exponential Equation'      => __( 'Exponential equations. For exp. 7^x = 9.' ),
					'Function'                  => __( 'Polynomial simplifications. For exp. (x-5)^2 * (x+5)^2.' ),
					'Function Composition'      => __( 'f(g(x)) when f(x)=x^2-2x, g(x)=2x-2' ),
					'Geometry'                  => __( 'A generic problem type that can be placed with other problem types. For exp. circle, ellipse, parabola, slope.' ),
					'Hyperbola'                 => __( 'Hyperbola problems. For exp. Find the x-intercept of (x^2)/4 - (y^2)/5 = 1.' ),
					'Inflection Point'          => __( 'Find the inflection point of f(x) = 1/2x^4 +x^3 - 6x^2.' ),
					'Integral'                  => __( 'Integral of sqrt (x^2 - y^2).' ),
					'Intercept'                 => __( 'Line intercept problems. For exp. Find the x-intercept of the line y = 10x - 5.' ),
					'Limit'                     => __( 'Limit problems. For exp. Find the limit of x as x approaches 1 for (x^2-1)/(x-1).' ),
					'Line Equation'             => __( 'Line equation problems. For exp. Find the equation of a line with points (-7,-4) and (-2,-6).' ),
					'Linear Algebra'            => __( 'A generic problem type that can be placed with other problem types. For exp. matrix and characteristic polynomial.' ),
					'Linear Equation'           => __( 'Linear equations. For exp. 4x - 3 = 2x + 9.' ),
					'Linear Inequality'         => __( 'Linear inequalities. For exp. 5x - 6 > 3x - 8.' ),
					'Logarithmic Equation'      => __( 'Logarithmic equations. For exp. log(x) = log(100).' ),
					'Logarithmic Inequality'    => __( 'Logarithmic inequalities. For exp. log(x) > log(100).' ),
					'Matrix'                    => __( '{{1,2,5}, {3,-1,1}, {1,2,3}} row reduce' ),
					'Midpoint'                  => __( 'Midpoint problems. For exp. find the midpoint between (-3, 7) and (5, -2).' ),
					'Parabola'                  => __( 'Parabola problems. For exp. Find the vertex of y2 - 4x - 4y = 0.' ),
					'Parallel'                  => __( 'Parallel line problems. For exp. Are the two lines parallel (y = 10x + 5, y = 20x + 10)?' ),
					'Perpendicular'             => __( 'Perpendicular problems. For exp. Are the two lines perpendicular (y = 10x + 5, y = 20x + 10)?' ),
					'Polynomial Equation'       => __( 'Polynomial equations. For exp. x^5 - 3x = 0.' ),
					'Polynomial Expression'     => __( 'Polynomial expressions. For exp. (x - 5)^4 * (x + 5)^2.' ),
					'Polynomial Inequality'     => __( 'Polynomial inequalities. For exp. x^4 - x^2 - 6 > x^3 - 3x^2.' ),
					'Quadratic Equation'        => __( 'Quadratic equations. For exp. x^2 - 3x - 4 = 0.' ),
					'Quadratic Expression'      => __( 'Quadratic expressions. For exp. x^2 - 3x - 2.' ),
					'Quadratic Inequality'      => __( 'Quadratic inequalities. For exp. x^2 - x - 6 > x^2 - 3x.' ),
					'Radical Equation'          => __( 'Radical equations. For exp. sqrt(x) - x = 0.' ),
					'Radical Inequality'        => __( 'Radical inequalities. For exp. sqrt(x) - x > 0.' ),
					'Rational Equation'         => __( 'Rational equations. For exp. 5/(x - 3) = 2/(x - 1).' ),
					'Rational Expression'       => __( 'Rational expressions. For exp. 1/(x^3 + 4x^2 + 5x + 2).' ),
					'Rational Inequality'       => __( 'Rational inequalities. For exp. 5/(x - 3) > 2/(x - 1).' ),
					'Slope'                     => __( 'Slope problems. For exp. Find the slope of y = 10x + 5.' ),
					'Statistics'                => __( 'Statistics problems. For exp. Find the mean of a set of numbers (3, 8, 2, 10).' ),
					'System of Equations'       => __( 'System of equations problems. For exp. Solve 2x + 5y = 16;3x - 5y = - 1.' ),
					'Trigonometry'              => __( 'Solve sin(t) + cos(t) = 1.' ),
				],
			],
			[
				'id'       => 'mathExpression-input',
				'label'    => __( 'Math expression input', 'slim-seo-schema' ),
				'required' => true,
				'tooltip'  => __( 'A mathematical expression (for exp. x^2-3x=0) that may be simplified, transformed, or solved for a specific variable. This can take many formats (for exp. LaTeX, Ascii-Math, or mathematical expressions that you can write with a keyboard).', 'slim-seo-schema' ),
			],
			[
				'id'       => 'target',
				'label'    => __( 'Target', 'slim-seo-schema' ),
				'required' => true,
				'tooltip'  => __( 'The URL target entrypoint for an action. Accepts a string to represent the math expression that\'s being solved by the action.', 'slim-seo-schema' ),
			],
		],
	],
	Helper::get_property( 'url', [
		'required' => true,
		'std'      => '{{ post.url }}',
		'tooltip'  => __( 'The URL of the Math solver.', 'slim-seo-schema' ),
	] ),
	[
		'id'       => 'usageInfo',
		'label'    => __( 'Usage info', 'slim-seo-schema' ),
		'required' => true,
		'tooltip'  => __( 'The privacy policy for your math problem solving site.', 'slim-seo-schema' ),
	],
];
