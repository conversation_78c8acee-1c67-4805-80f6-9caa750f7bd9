<?php
namespace SlimSEOPro\Schema\SchemaTypes;

return [
	[
		'id'   => 'googleDocs',
		'type' => 'GoogleDocs',
		'url'  => 'https://developers.google.com/search/docs/advanced/structured-data/review-snippet',
		'show' => true,
	],
	[
		'id'        => '@type',
		'label'     => __( 'Type', 'slim-seo-schema' ),
		'std'       => 'Review',
		'required'  => true,
		'dependant' => true,
		'type'      => 'DataList',
		'options'   => [
			'Review'          => __( 'Review', 'slim-seo-schema' ),
			'AggregateRating' => __( 'Aggregate rating', 'slim-seo-schema' ),
		],
	],
	Helper::get_property( 'Person', [
		'id'       => 'author',
		'label'    => __( 'Author', 'slim-seo-schema' ),
		'tooltip'  => __( 'The author of the review. Must be a valid name and should not exceed 100 characters.', 'slim-seo-schema' ),
		'required' => true,
	] ),
	Helper::get_property( 'name', [
		'show'    => true,
		'tooltip' => __( 'The title of the review author', 'slim-seo-schema' ),
	] ),
	[
		'id'       => 'itemReviewed',
		'label'    => __( 'Item reviewed', 'slim-seo-schema' ),
		'type'     => 'Group',
		'required' => true,
		'tooltip'  => __( 'The item that is being reviewed.', 'slim-seo-schema' ),
		'fields'   => [
			[
				'id'       => '@type',
				'label'    => __( 'Type', 'slim-seo-schema' ),
				'std'      => 'Product',
				'required' => true,
				'type'     => 'DataList',
				'options'  => [
					'Book'                => __( 'Book', 'slim-seo-schema' ),
					'Course'              => __( 'Course', 'slim-seo-schema' ),
					'CreativeWorkSeason'  => __( 'Creative work season', 'slim-seo-schema' ),
					'CreativeWorkSeries'  => __( 'Creative work series', 'slim-seo-schema' ),
					'Episode'             => __( 'Episode', 'slim-seo-schema' ),
					'Event'               => __( 'Event', 'slim-seo-schema' ),
					'Game'                => __( 'Game', 'slim-seo-schema' ),
					'HowTo'               => __( 'HowTo', 'slim-seo-schema' ),
					'LocalBusiness'       => __( 'Local business', 'slim-seo-schema' ),
					'MediaObject'         => __( 'Media object', 'slim-seo-schema' ),
					'Movie'               => __( 'Movie', 'slim-seo-schema' ),
					'MusicPlaylist'       => __( 'Music playlist', 'slim-seo-schema' ),
					'MusicRecording'      => __( 'Music recording', 'slim-seo-schema' ),
					'Organization'        => __( 'Organization', 'slim-seo-schema' ),
					'Product'             => __( 'Product', 'slim-seo-schema' ),
					'Recipe'              => __( 'Recipe', 'slim-seo-schema' ),
					'SoftwareApplication' => __( 'Software application', 'slim-seo-schema' ),
				],
			],
			Helper::get_property( 'name', [
				'std'      => '{{ post.title }}',
				'required' => true,
				'tooltip'  => __( 'The name of the item that is being reviewed.', 'slim-seo-schema' ),
			] ),
			Helper::get_property( 'address', [
				'tooltip' => __( 'The physical address where item is being reviewed.', 'slim-seo-schema' ),
			] ),
			Helper::get_property( 'image', [
				'tooltip' => __( 'The image of the item that is being reviewed.', 'slim-seo-schema' ),
			] ),
			[
				'id'      => 'priceRange',
				'label'   => __( 'Price range', 'slim-seo-schema' ),
				'tooltip' => __( 'The relative price range of a business, commonly specified by either a numerical range (for example, "$10-15") or a normalized number of currency signs (for example, "$$$"). This field must be shorter than 100 characters. If it\'s longer than 100 characters, Google won\'t show a price range for the business.', 'slim-seo-schema' ),
			],
			[
				'id'    => 'telephone',
				'label' => __( 'Telephone', 'slim-seo-schema' ),
			],
		],
	],
	[
		'id'      => 'positiveNotes',
		'type'    => 'Group',
		'label'   => __( 'Positive notes (Pros)', 'slim-seo-schema' ),
		'tooltip' => __( 'A list of positive statements about the product, listed in a specific order', 'slim-seo-schema' ),
		'show'    => true,
		'fields'  => [
			[
				'id'       => '@type',
				'std'      => 'ItemList',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'        => 'itemListElement',
				'type'      => 'Group',
				'cloneable' => true,
				'show'      => true,
				'fields'    => [
					[
						'id'       => '@type',
						'std'      => 'ListItem',
						'type'     => 'Hidden',
						'required' => true,
					],
					Helper::get_property( 'name', [
						'std'      => '',
						'tooltip'  => __( 'The key statement of the review.', 'slim-seo-schema' ),
						'required' => true,
					] ),
					[
						'id'      => 'position',
						'label'   => __( 'Position', 'slim-seo-schema' ),
						'show'    => true,
						'tooltip' => __( 'The position (order) of the statement in the list.', 'slim-seo-schema' ),
					],
				],
			],
		],
	],
	[
		'id'      => 'negativeNotes',
		'type'    => 'Group',
		'label'   => __( 'Negative notes (Cons)', 'slim-seo-schema' ),
		'tooltip' => __( 'A list of negative statements about the product, listed in a specific order', 'slim-seo-schema' ),
		'show'    => true,
		'fields'  => [
			[
				'id'       => '@type',
				'std'      => 'ItemList',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'        => 'itemListElement',
				'type'      => 'Group',
				'cloneable' => true,
				'show'      => true,
				'fields'    => [
					[
						'id'       => '@type',
						'std'      => 'ListItem',
						'type'     => 'Hidden',
						'required' => true,
					],
					Helper::get_property( 'name', [
						'std'      => '',
						'tooltip'  => __( 'The key statement of the review.', 'slim-seo-schema' ),
						'required' => true,
					] ),
					[
						'id'      => 'position',
						'label'   => __( 'Position', 'slim-seo-schema' ),
						'show'    => true,
						'tooltip' => __( 'The position (order) of the statement in the list.', 'slim-seo-schema' ),
					],
				],
			],
		],
	],
	[
		'id'         => 'reviewRating',
		'label'      => __( 'Rating', 'slim-seo-schema' ),
		'dependency' => '[@type]:Review',
		'type'       => 'Group',
		'show'       => true,
		'fields'     => [
			[
				'id'       => '@type',
				'std'      => 'Rating',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'       => 'ratingValue',
				'label'    => __( 'Rating value', 'slim-seo-schema' ),
				'required' => true,
				'std'      => 5,
			],
			[
				'id'      => 'bestRating',
				'label'   => __( 'Best rating', 'slim-seo-schema' ),
				'type'    => 'DataList',
				'std'     => 5,
				'options' => [
					1 => 1,
					2 => 2,
					3 => 3,
					4 => 4,
					5 => 5,
				],
			],
			[
				'id'      => 'worstRating',
				'label'   => __( 'Worst rating', 'slim-seo-schema' ),
				'type'    => 'DataList',
				'std'     => 1,
				'options' => [
					1 => 1,
					2 => 2,
					3 => 3,
					4 => 4,
					5 => 5,
				],
			],
		],
	],
	[
		'id'         => 'ratingCount',
		'label'      => __( 'Rating count', 'slim-seo-schema' ),
		'dependency' => '[@type]:AggregateRating',
		'tooltip'    => __( 'The total number of ratings for the item on your site.', 'slim-seo-schema' ),
		'required'   => true,
	],
	[
		'id'         => 'ratingValue',
		'dependency' => '[@type]:AggregateRating',
		'label'      => __( 'Rating value', 'slim-seo-schema' ),
		'tooltip'    => __( 'A numerical quality rating for the organization, either a number, fraction, or percentage (for exp. "4", "60%", or "6 / 10").', 'slim-seo-schema' ),
		'required'   => true,
	],
	[
		'id'         => 'reviewCount',
		'dependency' => '[@type]:AggregateRating',
		'label'      => __( 'Review count', 'slim-seo-schema' ),
		'tooltip'    => __( 'Specifies the number of people who provided a review with or without an accompanying rating. At least one of ratingCount or reviewCount is required.', 'slim-seo-schema' ),
		'required'   => true,
	],
	[
		'id'      => 'reviewBody',
		'label'   => __( 'Review body', 'slim-seo-schema' ),
		'show'    => true,
		'tooltip' => __( 'The actual body of the review.', 'slim-seo-schema' ),
	],
	[
		'id'         => 'bestRating',
		'label'      => __( 'Best rating', 'slim-seo-schema' ),
		'dependency' => '[@type]:AggregateRating',
		'type'       => 'DataList',
		'tooltip'    => __( 'The highest value allowed in this rating system.', 'slim-seo-schema' ),
		'std'        => 5,
		'options'    => [
			1 => 1,
			2 => 2,
			3 => 3,
			4 => 4,
			5 => 5,
		],
	],
	[
		'id'         => 'worstRating',
		'label'      => __( 'Worst rating', 'slim-seo-schema' ),
		'dependency' => '[@type]:AggregateRating',
		'type'       => 'DataList',
		'tooltip'    => __( 'The lowest value allowed in this rating system.', 'slim-seo-schema' ),
		'std'        => 1,
		'options'    => [
			1 => 1,
			2 => 2,
			3 => 3,
			4 => 4,
			5 => 5,
		],
	],
	[
		'id'    => 'datePublished',
		'label' => __( 'Date published', 'slim-seo-schema' ),
		'show'  => true,
		'std'   => '{{ post.date }}',
		'type'  => 'Date',
	],
];
