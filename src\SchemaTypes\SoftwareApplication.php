<?php
namespace SlimSEOPro\Schema\SchemaTypes;

return [
	[
		'id'   => 'googleDocs',
		'type' => 'GoogleDocs',
		'url'  => 'https://developers.google.com/search/docs/advanced/structured-data/software-app',
		'show' => true,
	],
	Helper::get_property( 'name', [
		'required' => true,
		'tooltip'  => __( 'The name of the app', 'slim-seo-schema' ),
	] ),
	Helper::get_property( 'aggregateRating', [
		'required' => true,
	] ),
	[
		'id'       => 'offers',
		'label'    => __( 'Offers', 'slim-seo-schema' ),
		'tooltip'  => __( 'An offer to sell the app.', 'slim-seo-schema' ),
		'type'     => 'Group',
		'required' => true,
		'fields'   => [
			[
				'id'       => '@type',
				'std'      => 'Offer',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'       => 'price',
				'label'    => __( 'Price', 'slim-seo-schema' ),
				'tooltip'  => __( 'The price of the app.', 'slim-seo-schema' ),
				'required' => true,
			],
			[
				'id'      => 'priceCurrency',
				'std'     => 'USD',
				'label'   => __( 'Price currency', 'slim-seo-schema' ),
				'tooltip' => __( 'Include if the app has a price greater than 0.', 'slim-seo-schema' ),
				'show'    => true,
			],
		],
	],
	[
		'id'      => 'applicationCategory',
		'label'   => __( 'Application category', 'slim-seo-schema' ),
		'std'     => 'XML',
		'type'    => 'DataList',
		'std'     => 'GameApplication',
		'tooltip' => __( 'The type of app.', 'slim-seo-schema' ),
		'options' => [
			'GameApplication'               => __( 'Game application', 'slim-seo-schema' ),
			'SocialNetworkingApplication'   => __( 'Social networking application', 'slim-seo-schema' ),
			'TravelApplication'             => __( 'Travel application', 'slim-seo-schema' ),
			'ShoppingApplication'           => __( 'Shopping application', 'slim-seo-schema' ),
			'SportsApplication'             => __( 'Sports application', 'slim-seo-schema' ),
			'LifestyleApplication'          => __( 'Lifestyle application', 'slim-seo-schema' ),
			'BusinessApplication'           => __( 'Business application', 'slim-seo-schema' ),
			'DesignApplication'             => __( 'Design application', 'slim-seo-schema' ),
			'DeveloperApplication'          => __( 'Developer application', 'slim-seo-schema' ),
			'DriverApplication'             => __( 'Driver application', 'slim-seo-schema' ),
			'EducationalApplication'        => __( 'Educational application', 'slim-seo-schema' ),
			'HealthApplication'             => __( 'Health application', 'slim-seo-schema' ),
			'FinanceApplication'            => __( 'Finance application', 'slim-seo-schema' ),
			'SecurityApplication'           => __( 'Security application', 'slim-seo-schema' ),
			'BrowserApplication'            => __( 'Browser application', 'slim-seo-schema' ),
			'CommunicationApplication'      => __( 'Communication application', 'slim-seo-schema' ),
			'DesktopEnhancementApplication' => __( 'Desktop enhancement application', 'slim-seo-schema' ),
			'EntertainmentApplication'      => __( 'Entertainment application', 'slim-seo-schema' ),
			'MultimediaApplication'         => __( 'Multimedia application', 'slim-seo-schema' ),
			'HomeApplication'               => __( 'Home application', 'slim-seo-schema' ),
			'UtilitiesApplication'          => __( 'Utilities application', 'slim-seo-schema' ),
			'ReferenceApplication'          => __( 'Reference application', 'slim-seo-schema' ),
		],
	],
	[
		'id'      => 'operatingSystem',
		'label'   => __( 'Operating system', 'slim-seo-schema' ),
		'tooltip' => __( 'The operating system(s) required to use the app (for exp. Windows 7, OSX 10.6, Android 1.6)', 'slim-seo-schema' ),
	],
	Helper::get_property( 'Review', [
		'required' => true,
	] ),
];
