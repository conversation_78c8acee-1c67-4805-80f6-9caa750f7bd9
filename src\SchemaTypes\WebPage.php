<?php
namespace SlimSEOPro\Schema\SchemaTypes;

return [
	[
		'id'       => '@type',
		'required' => true,
		'label'    => __( 'Type', 'slim-seo-schema' ),
		'type'     => 'DataList',
		'std'      => 'WebPage',
		'options'  => [
			'WebPage'           => __( 'General web page', 'slim-seo-schema' ),
			'AboutPage'         => __( 'About page', 'slim-seo-schema' ),
			'CheckoutPage'      => __( 'Checkout page', 'slim-seo-schema' ),
			'CollectionPage'    => __( 'Collection page', 'slim-seo-schema' ),
			'ContactPage'       => __( 'Contact page', 'slim-seo-schema' ),
			'ItemPage'          => __( 'Item page', 'slim-seo-schema' ),
			'MedicalWebPage'    => __( 'Medical web page', 'slim-seo-schema' ),
			'ProfilePage'       => __( 'Profile page', 'slim-seo-schema' ),
			'QAPage'            => __( 'QA page', 'slim-seo-schema' ),
			'RealEstateListing' => __( 'Real estate listing', 'slim-seo-schema' ),
			'SearchResultsPage' => __( 'Search results', 'slim-seo-schema' ),
		],
	],
	Helper::get_property( 'name', [
		'required' => true,
		'std'      => '{{ current.title }}',
		'tooltip'  => __( 'The name of the webpage.', 'slim-seo-schema' ),
	] ),
	[
		'id'      => 'author',
		'label'   => __( 'Author', 'slim-seo-schema' ),
		'tooltip' => __( 'The author of the page.', 'slim-seo-schema' ),
		'std'     => '{{ schemas.person }}',
	],
	[
		'id'      => 'about',
		'label'   => __( 'About', 'slim-seo-schema' ),
		'tooltip' => __( 'The subject matter of the content.', 'slim-seo-schema' ),
		'type'    => 'Group',
		'fields'  => [
			[
				'id'       => '@type',
				'std'      => 'Thing',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'label'    => __( 'Name', 'slim-seo-schema' ),
				'id'       => 'name',
				'required' => true,
			],
			Helper::get_property( 'description', [
				'std'     => '',
				'tooltip' => __( 'A description of the item.', 'slim-seo-schema' ),
			] ),
		],
	],
	[
		'id'      => 'abstract',
		'label'   => __( 'Abstract', 'slim-seo-schema' ),
		'tooltip' => __( 'A short description that summarizes the page.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'accessMode',
		'label'   => __( 'Access mode', 'slim-seo-schema' ),
		'tooltip' => __( 'The human sensory perceptual system or cognitive faculty through which a person may process or perceive information.', 'slim-seo-schema' ),
		'type'    => 'DataList',
		'options' => [
			'auditory'        => __( 'Auditory', 'slim-seo-schema' ),
			'chartOnVisual'   => __( 'Chart on visual', 'slim-seo-schema' ),
			'chemOnVisual'    => __( 'Chem on visual', 'slim-seo-schema' ),
			'colorDependent'  => __( 'Color dependent', 'slim-seo-schema' ),
			'diagramOnVisual' => __( 'Diagram on visual', 'slim-seo-schema' ),
			'mathOnVisual'    => __( 'Math on visual', 'slim-seo-schema' ),
			'musicOnVisual'   => __( 'Music on visual', 'slim-seo-schema' ),
			'tactile'         => __( 'Tactile', 'slim-seo-schema' ),
			'textOnVisual'    => __( 'Text on visual', 'slim-seo-schema' ),
			'textual'         => __( 'Textual', 'slim-seo-schema' ),
			'visual'          => __( 'Visual', 'slim-seo-schema' ),
		],
	],
	[
		'id'      => 'accessModeSufficient',
		'type'    => 'Group',
		'label'   => __( 'Access mode sufficient', 'slim-seo-schema' ),
		'tooltip' => __( 'A list of single or combined accessModes that are sufficient to understand all the intellectual content of a resource. Values should be drawn from the approved vocabulary.', 'slim-seo-schema' ),
		'fields'  => [
			[
				'id'       => '@type',
				'std'      => 'ItemList',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'        => 'itemListElement',
				'type'      => 'Group',
				'cloneable' => true,
				'show'      => true,
				'fields'    => [
					[
						'id'       => '@type',
						'std'      => 'ListItem',
						'type'     => 'Hidden',
						'required' => true,
					],
					[
						'id'       => 'name',
						'label'    => __( 'Name', 'slim-seo-schema' ),
						'tooltip'  => __( 'The key statement of the review.', 'slim-seo-schema' ),
						'type'     => 'DataList',
						'required' => true,
						'options'  => [
							'auditory'        => __( 'Auditory', 'slim-seo-schema' ),
							'chartOnVisual'   => __( 'Chart on visual', 'slim-seo-schema' ),
							'chemOnVisual'    => __( 'Chem on visual', 'slim-seo-schema' ),
							'colorDependent'  => __( 'Color dependent', 'slim-seo-schema' ),
							'diagramOnVisual' => __( 'Diagram on visual', 'slim-seo-schema' ),
							'mathOnVisual'    => __( 'Math on visual', 'slim-seo-schema' ),
							'musicOnVisual'   => __( 'Music on visual', 'slim-seo-schema' ),
							'tactile'         => __( 'Tactile', 'slim-seo-schema' ),
							'textOnVisual'    => __( 'Text on visual', 'slim-seo-schema' ),
							'textual'         => __( 'Textual', 'slim-seo-schema' ),
							'visual'          => __( 'Visual', 'slim-seo-schema' ),
						],
					],
					[
						'id'      => 'position',
						'label'   => __( 'Position', 'slim-seo-schema' ),
						'show'    => true,
						'tooltip' => __( 'The position (order) of the statement in the list.', 'slim-seo-schema' ),
					],
				],
			],
		],
	],
	[
		'id'      => 'accessibilityAPI',
		'label'   => __( 'Accessibility API', 'slim-seo-schema' ),
		'tooltip' => __( 'Indicates that the resource is compatible with the referenced accessibility API.', 'slim-seo-schema' ),
		'type'    => 'DataList',
		'options' => [
			'auditory'        => __( 'Auditory', 'slim-seo-schema' ),
			'chartOnVisual'   => __( 'Chart on visual', 'slim-seo-schema' ),
			'chemOnVisual'    => __( 'Chem on visual', 'slim-seo-schema' ),
			'colorDependent'  => __( 'Color dependent', 'slim-seo-schema' ),
			'diagramOnVisual' => __( 'Diagram on visual', 'slim-seo-schema' ),
			'mathOnVisual'    => __( 'Math on visual', 'slim-seo-schema' ),
			'musicOnVisual'   => __( 'Music on visual', 'slim-seo-schema' ),
			'tactile'         => __( 'Tactile', 'slim-seo-schema' ),
			'textOnVisual'    => __( 'Text on visual', 'slim-seo-schema' ),
			'textual'         => __( 'Textual', 'slim-seo-schema' ),
			'visual'          => __( 'Visual', 'slim-seo-schema' ),
		],
	],
	[
		'id'      => 'accessibilityControl',
		'label'   => __( 'Accessibility control', 'slim-seo-schema' ),
		'tooltip' => __( 'Identifies input methods that are sufficient to fully control the described resource.', 'slim-seo-schema' ),
		'type'    => 'DataList',
		'options' => [
			'auditory'        => __( 'Auditory', 'slim-seo-schema' ),
			'chartOnVisual'   => __( 'Chart on visual', 'slim-seo-schema' ),
			'chemOnVisual'    => __( 'Chem on visual', 'slim-seo-schema' ),
			'colorDependent'  => __( 'Color dependent', 'slim-seo-schema' ),
			'diagramOnVisual' => __( 'Diagram on visual', 'slim-seo-schema' ),
			'mathOnVisual'    => __( 'Math on visual', 'slim-seo-schema' ),
			'musicOnVisual'   => __( 'Music on visual', 'slim-seo-schema' ),
			'tactile'         => __( 'Tactile', 'slim-seo-schema' ),
			'textOnVisual'    => __( 'Text on visual', 'slim-seo-schema' ),
			'textual'         => __( 'Textual', 'slim-seo-schema' ),
			'visual'          => __( 'Visual', 'slim-seo-schema' ),
		],
	],
	[
		'id'      => 'accessibilityFeature',
		'label'   => __( 'Accessibility feature', 'slim-seo-schema' ),
		'tooltip' => __( 'Content features of the resource, such as accessible media, alternatives and supported enhancements for accessibility.', 'slim-seo-schema' ),
		'type'    => 'DataList',
		'options' => [
			'auditory'        => __( 'Auditory', 'slim-seo-schema' ),
			'chartOnVisual'   => __( 'Chart on visual', 'slim-seo-schema' ),
			'chemOnVisual'    => __( 'Chem on visual', 'slim-seo-schema' ),
			'colorDependent'  => __( 'Color dependent', 'slim-seo-schema' ),
			'diagramOnVisual' => __( 'Diagram on visual', 'slim-seo-schema' ),
			'mathOnVisual'    => __( 'Math on visual', 'slim-seo-schema' ),
			'musicOnVisual'   => __( 'Music on visual', 'slim-seo-schema' ),
			'tactile'         => __( 'Tactile', 'slim-seo-schema' ),
			'textOnVisual'    => __( 'Text on visual', 'slim-seo-schema' ),
			'textual'         => __( 'Textual', 'slim-seo-schema' ),
			'visual'          => __( 'Visual', 'slim-seo-schema' ),
		],
	],
	[
		'id'      => 'accessibilityHazard',
		'label'   => __( 'Accessibility hazard', 'slim-seo-schema' ),
		'tooltip' => __( 'A characteristic of the described resource that is physiologically dangerous to some users. Related to WCAG 2.0 guideline 2.3.', 'slim-seo-schema' ),
		'type'    => 'DataList',
		'options' => [
			'auditory'        => __( 'Auditory', 'slim-seo-schema' ),
			'chartOnVisual'   => __( 'Chart on visual', 'slim-seo-schema' ),
			'chemOnVisual'    => __( 'Chem on visual', 'slim-seo-schema' ),
			'colorDependent'  => __( 'Color dependent', 'slim-seo-schema' ),
			'diagramOnVisual' => __( 'Diagram on visual', 'slim-seo-schema' ),
			'mathOnVisual'    => __( 'Math on visual', 'slim-seo-schema' ),
			'musicOnVisual'   => __( 'Music on visual', 'slim-seo-schema' ),
			'tactile'         => __( 'Tactile', 'slim-seo-schema' ),
			'textOnVisual'    => __( 'Text on visual', 'slim-seo-schema' ),
			'textual'         => __( 'Textual', 'slim-seo-schema' ),
			'visual'          => __( 'Visual', 'slim-seo-schema' ),
		],
	],
	[
		'id'      => 'accessibilitySummary',
		'label'   => __( 'Accessibility summary', 'slim-seo-schema' ),
		'tooltip' => __( 'A human-readable summary of specific accessibility features or deficiencies, consistent with the other accessibility metadata but expressing subtleties.', 'slim-seo-schema' ),
	],
	[
		'id'          => 'accountablePerson',
		'label'       => __( 'Accountable person', 'slim-seo-schema' ),
		'tooltip'     => __( 'Specifies the Person that is legally accountable for this web page', 'slim-seo-schema' ),
		'description' => __( 'Please create a Person or an Organization schema and link to this property via a dynamic variable', 'slim-seo-schema' ),
	],
	[
		'id'      => 'acquireLicensePage',
		'label'   => __( 'Acquire license page', 'slim-seo-schema' ),
		'tooltip' => __( 'Indicates a page documenting how licenses can be purchased or otherwise acquired, for the current item.', 'slim-seo-schema' ),
	],
	Helper::get_property( 'aggregateRating' ),
	[
		'id'      => 'alternativeHeadline',
		'label'   => __( 'Alternative headline', 'slim-seo-schema' ),
		'tooltip' => __( 'A secondary title of the page.', 'slim-seo-schema' ),
	],
	[
		'label' => __( 'Breadcrumbs', 'slim-seo-schema' ),
		'id'    => 'breadcrumb',
		'std'   => '{{ schemas.breadcrumblist }}',
		'show'  => true,
	],
	Helper::get_property( 'url', [
		'id'      => 'archivedAt',
		'label'   => __( 'Archived at', 'slim-seo-schema' ),
		'tooltip' => __( 'Indicates a page or other link involved in archival of the page.', 'slim-seo-schema' ),
	] ),
	[
		'id'      => 'assesses',
		'label'   => __( 'Assesses', 'slim-seo-schema' ),
		'tooltip' => __( 'The item being described is intended to assess the competency or learning outcome defined by the referenced term.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Comment count', 'slim-seo-schema' ),
		'id'      => 'commentCount',
		'std'     => '{{ post.comment_count }}',
		'tooltip' => __( 'The number of comments.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Conditions of access', 'slim-seo-schema' ),
		'id'      => 'conditionsOfAccess',
		'tooltip' => __( 'Conditions that affect the availability of, or method(s) of access to, an item. For exp "Available by appointment from the Reading Room" or "Accessible only from logged-in accounts.".', 'slim-seo-schema' ),
	],
	[
		'id'      => 'contentLocation',
		'label'   => __( 'Content location', 'slim-seo-schema' ),
		'type'    => 'Group',
		'tooltip' => __( 'The location depicted or described in the content. For example, the location in a photograph or painting.', 'slim-seo-schema' ),
		'fields'  => [
			[
				'id'       => '@type',
				'std'      => 'Place',
				'type'     => 'Hidden',
				'required' => true,
			],
			Helper::get_property( 'address', [
				'label'            => '',
				'required'         => true,
				'cloneable'        => true,
				'cloneItemHeading' => __( 'Address', 'slim-seo-schema' ),
			] ),
		],
	],
	[
		'label'   => __( 'Copyright notice', 'slim-seo-schema' ),
		'id'      => 'copyrightNotice',
		'tooltip' => __( 'Text of a notice appropriate for describing the copyright aspects of this page, ideally indicating the owner of the copyright for the page.".', 'slim-seo-schema' ),
	],
	[
		'id'      => 'copyrightYear',
		'label'   => __( 'Copyright year', 'slim-seo-schema' ),
		'tooltip' => __( 'The year during which the claimed copyright for the page was first asserted.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Country of origin', 'slim-seo-schema' ),
		'id'      => 'countryOfOrigin',
		'type'    => 'Group',
		'tooltip' => __( 'The country of origin of the page.', 'slim-seo-schema' ),
		'fields'  => [
			[
				'id'   => '@type',
				'std'  => 'Country',
				'type' => 'Hidden',
			],
			[
				'id'      => 'address',
				'label'   => __( 'Address', 'slim-seo-schema' ),
				'tooltip' => __( 'Physical address of the item.', 'slim-seo-schema' ),
			],
			[
				'id'      => 'branchCode',
				'label'   => __( 'Branch code', 'slim-seo-schema' ),
				'tooltip' => __( 'A short textual code (also called "store code") that uniquely identifies a place of business. The code is typically assigned by the parentOrganization and used in structured URLs.', 'slim-seo-schema' ),
			],
			[
				'id'      => 'description',
				'label'   => __( 'Description', 'slim-seo-schema' ),
				'tooltip' => __( 'A description of the country.', 'slim-seo-schema' ),
			],
			[
				'id'    => 'faxNumber',
				'label' => __( 'Fax number', 'slim-seo-schema' ),
			],
			[
				'id'      => 'globalLocationNumber',
				'label'   => __( 'Global location number', 'slim-seo-schema' ),
				'tooltip' => __( 'The GLN is a 13-digit number used to identify parties and physical locations.', 'slim-seo-schema' ),
			],
			[
				'id'      => 'hasMap',
				'label'   => __( 'Has Map', 'slim-seo-schema' ),
				'tooltip' => __( 'A URL to a map of the place.', 'slim-seo-schema' ),
			],
			[
				'id'      => 'latitude',
				'label'   => __( 'Latitude', 'slim-seo-schema' ),
				'tooltip' => __( 'The latitude of a location. For example 37.42242 (WGS 84).', 'slim-seo-schema' ),
			],
			[
				'id'      => 'longitude',
				'label'   => __( 'Longitude', 'slim-seo-schema' ),
				'tooltip' => __( 'The longitude of a location. For example -122.08585 (WGS 84).', 'slim-seo-schema' ),
			],
			[
				'id'      => 'logo',
				'label'   => __( 'Logo', 'slim-seo-schema' ),
				'tooltip' => __( 'Link to an associated logo.', 'slim-seo-schema' ),
			],
			[
				'id'      => 'name',
				'label'   => __( 'Name', 'slim-seo-schema' ),
				'tooltip' => __( 'The name of the country.', 'slim-seo-schema' ),
				'show'    => true,
			],
			[
				'id'    => 'telephone',
				'label' => __( 'Telephone number', 'slim-seo-schema' ),
			],
		],
	],
	[
		'id'      => 'creativeWorkStatus',
		'label'   => __( 'Creative work status', 'slim-seo-schema' ),
		'tooltip' => __( 'The status of the page in terms of its stage in a lifecycle. Exp terms include Incomplete, Draft, Published, Obsolete.', 'slim-seo-schema' ),
	],
	[
		'id'          => 'contributor',
		'label'       => __( 'Contributor', 'slim-seo-schema' ),
		'std'         => '{{ schemas.person }}',
		'tooltip'     => __( 'A secondary contributor to the page', 'slim-seo-schema' ),
		'description' => __( 'Please create a Person or an Organization schema and link to this property via a dynamic variable', 'slim-seo-schema' ),
	],
	[
		'id'          => 'copyrightHolder',
		'label'       => __( 'Copyright holder', 'slim-seo-schema' ),
		'std'         => '{{ schemas.person }}',
		'tooltip'     => __( 'The party holding the legal copyright to the page.', 'slim-seo-schema' ),
		'description' => __( 'Please create a Person or an Organization schema and link to this property via a dynamic variable', 'slim-seo-schema' ),
	],
	Helper::get_property( 'dateCreated', [
		'tooltip' => __( 'Date the page was created, in ISO 8601 format.', 'slim-seo-schema' ),
		'std'     => '{{ post.date }}',
	] ),
	Helper::get_property( 'datePublished', [
		'tooltip' => __( 'Date of first broadcast/publication, in ISO 8601 format', 'slim-seo-schema' ),
		'std'     => '{{ post.date }}',
	] ),
	[
		'id'      => 'description',
		'show'    => true,
		'label'   => __( 'Description', 'slim-seo-schema' ),
		'tooltip' => __( 'A description of the webpage.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'expires',
		'label'   => __( 'Expires', 'slim-seo-schema' ),
		'tooltip' => __( 'Date the content expires and is no longer useful or available, in ISO 8601 format. Don\'t supply this information if the page does not expire.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'inLanguage',
		'label'   => __( 'In language', 'slim-seo-schema' ),
		'tooltip' => __( 'The language of the content or performance or used in an action. Please use one of the language codes from the IETF BCP 47 standard.', 'slim-seo-schema' ),
		'std'     => '{{ site.language }}',
		'show'    => true,
	],
	[
		'id'      => 'isAccessibleForFree',
		'label'   => __( 'Is accessible for free?', 'slim-seo-schema' ),
		'tooltip' => __( 'A flag to signal that the page is accessible for free.', 'slim-seo-schema' ),
		'type'    => 'DataList',
		'std'     => 'true',
		'options' => [
			'true'  => __( 'Yes', 'slim-seo-schema' ),
			'false' => __( 'No', 'slim-seo-schema' ),
		],
	],
	[
		'id'      => 'isFamilyFriendly',
		'label'   => __( 'Is family friendly', 'slim-seo-schema' ),
		'tooltip' => __( 'Indicates whether the page is family friendly.', 'slim-seo-schema' ),
		'type'    => 'DataList',
		'std'     => 'True',
		'options' => [
			'True'  => __( 'True', 'slim-seo-schema' ),
			'False' => __( 'False', 'slim-seo-schema' ),
		],
	],
	[
		'label'   => __( 'Keywords', 'slim-seo-schema' ),
		'id'      => 'keywords',
		'tooltip' => __( 'Keywords or tags used to describe the page. Multiple textual entries in a keywords list are typically delimited by commas.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'license',
		'label'   => __( 'License', 'slim-seo-schema' ),
		'tooltip' => __( 'A license document that applies to the page, typically indicated by URL.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Last reviewed', 'slim-seo-schema' ),
		'id'      => 'lastReviewed',
		'tooltip' => __( 'Date on which the content on this web page was last reviewed for accuracy and/or completeness (in YYYY-MM-DD or YYYY format).', 'slim-seo-schema' ),
	],
	[
		'label' => __( 'Is part of', 'slim-seo-schema' ),
		'id'    => 'isPartOf',
		'std'   => '{{ schemas.website }}',
		'show'  => true,
	],
	[
		'id'      => 'publisher',
		'label'   => __( 'Publisher', 'slim-seo-schema' ),
		'std'     => '{{ schemas.organization }}',
		'tooltip' => __( 'The publisher of the page.', 'slim-seo-schema' ),
		'show'    => true,
	],
	[
		'id'               => 'mainContentOfPage',
		'label'            => __( 'Main content of page', 'slim-seo-schema' ),
		'type'             => 'Group',
		'tooltip'          => __( 'Indicates if this web page element is the main subject of the page.', 'slim-seo-schema' ),
		'cloneable'        => true,
		'cloneItemHeading' => __( 'Part', 'slim-seo-schema' ),
		'fields'           => [
			[
				'id'       => '@type',
				'std'      => 'WebPageElement',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'       => 'isAccessibleForFree',
				'label'    => __( 'Is accessible for free?', 'slim-seo-schema' ),
				'tooltip'  => __( 'Whether the dataset is accessible without payment.', 'slim-seo-schema' ),
				'type'     => 'DataList',
				'std'      => 'true',
				'options'  => [
					'true'  => __( 'Yes', 'slim-seo-schema' ),
					'false' => __( 'No', 'slim-seo-schema' ),
				],
				'required' => true,
			],
			[
				'id'       => 'cssSelector',
				'label'    => __( 'CSS selector', 'slim-seo-schema' ),
				'tooltip'  => __( 'Class name around each paywalled section of your page.', 'slim-seo-schema' ),
				'required' => true,
			],
		],
	],
	Helper::get_property( 'url', [
		'id'      => 'relatedLink',
		'label'   => __( 'Related link', 'slim-seo-schema' ),
		'tooltip' => __( 'A link related to this web page, for exp. to other related web pages.', 'slim-seo-schema' ),
	] ),
	[
		'id'      => 'significantLink',
		'label'   => __( 'Significant link', 'slim-seo-schema' ),
		'tooltip' => __( 'One of the more significant URLs on the page. Typically, these are the non-navigation links that are clicked on the most.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'speakable',
		'label'   => __( 'Speakable', 'slim-seo-schema' ),
		'type'    => 'Group',
		'tooltip' => __( 'Indicates sections of a Web page that are particularly \'speakable\' in the sense of being highlighted as being especially appropriate for text-to-speech conversion. Can be CSS selectors or XPaths.', 'slim-seo-schema' ),
		'fields'  => [
			[
				'id'       => '@type',
				'std'      => 'SpeakableSpecification',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'      => 'cssSelector',
				'label'   => __( 'CSS selector', 'slim-seo-schema' ),
				'tooltip' => __( 'Addresses content in the annotated pages (such as class attribute). Use either CSS selector or xPath; don\'t use both.', 'slim-seo-schema' ),
				'show'    => true,
			],
			[
				'id'      => 'xPath',
				'label'   => __( 'xPath', 'slim-seo-schema' ),
				'tooltip' => __( 'Addresses content using xPaths (assuming an XML view of the content). Use either CSS selector or xPath; don\'t use both.', 'slim-seo-schema' ),
				'show'    => true,
			],
		],
	],
	[
		'id'      => 'thumbnailUrl',
		'label'   => __( 'Thumbnail URL', 'slim-seo-schema' ),
		'tooltip' => __( 'A thumbnail image relevant to the page.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'version',
		'label'   => __( 'Version', 'slim-seo-schema' ),
		'tooltip' => __( 'The version number of the page.', 'slim-seo-schema' ),
	],
	Helper::get_property( 'url', [
		'required' => true,
		'std'      => '{{ current.url }}',
	] ),
];
